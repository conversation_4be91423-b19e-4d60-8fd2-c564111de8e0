import { useCallback, useEffect, useRef, useState } from "react";
import { FETCH_REQUEST_TYPE } from "@/entrypoints/background/handlers/fetchMessageHandler.ts";
import { FETCH_SSE_PORT_NAME } from "@/entrypoints/background/handlers/fetchSSEMessageHandler.ts";
import { IFetchSSEArguments, IFetchSSEResponseMessage } from "@/types/message";
import { Runtime } from "webextension-polyfill";
import Port = Runtime.Port;

export const useFetchSSE = () => {
  // 用于存储 浏览器扩展的 Port 连接，这样 Port 在组件重新渲染时不会丢失
  const portRef = useRef<Port | null>(null);
  // 用于 判断 Port 是否是手动关闭的 ，防止自动重连
  const [isPortManuallyClosed, setIsPortManuallyClosed] = useState(false);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isConnectingRef = useRef(false);

  const handlePort = useCallback(() => {
    // 防止重复连接
    if (isConnectingRef.current || portRef.current) {
      return;
    }

    isConnectingRef.current = true;

    try {
      // 创建一个与 background.js 的长连接 ，用于持续通信。
      const newPort = browser.runtime.connect(browser.runtime.id, { name: FETCH_SSE_PORT_NAME });

      // 如果不是手动关闭 ，就 延迟重新连接 ，确保 SSE 连接的稳定性。
      newPort.onDisconnect.addListener((port) => {
        console.debug("background中断了当前端口 ", port);
        isConnectingRef.current = false;

        // 清理当前端口引用
        if (portRef.current === newPort) {
          portRef.current = null;
        }

        // 如果不是手动关闭且组件未卸载，则重连
        if (!isPortManuallyClosed) {
          // 清除之前的重连定时器
          if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
          }

          reconnectTimeoutRef.current = setTimeout(() => {
            console.debug("尝试重新连接...");
            handlePort();
          }, 1000); // 增加重连间隔到1秒，避免频繁重连
        }
      });

      portRef.current = newPort;
      isConnectingRef.current = false;
    } catch (error) {
      console.error("创建SSE端口连接失败:", error);
      isConnectingRef.current = false;
    }
  }, [isPortManuallyClosed]);

  useEffect(() => {
    handlePort(); // 组件挂载时，建立 Port 连接
    return () => {
      // 清理重连定时器
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }

      // 断开端口连接
      if (portRef.current) {
        portRef.current.disconnect();
        portRef.current = null;
      }

      setIsPortManuallyClosed(true); // 标记手动关闭，防止自动重连
      isConnectingRef.current = false;
    };
  }, [handlePort]);

  // return useCallback((args: IFetchSSEArguments) => {
  //   if (!portRef.current) handlePort(); // 确保 Port 存在

  //   const removeMessageSSEHandler = () => {
  //     portRef.current.onMessage.removeListener(messageSSEHandler);
  //   };

  //   // 定义 messageSSEHandler 监听返回的 SSE 消息
  //   const messageSSEHandler = (res: IFetchSSEResponseMessage) => {
  //     // if (res.type !== FETCH_RESPONSE_TYPE) return;
  //     // console.debug("content接受到了background的SSE消息：", res);
  //     if (res.instruct === "stop") {
  //       args.callback({ process: res.process });
  //       removeMessageSSEHandler();
  //     } else {
  //       switch (res.process) {
  //         case "onmessage":
  //           args.callback({ ...res, process: res.process });
  //           break;
  //         case "onmessagefile":
  //           args.callback({ ...res, process: res.process });
  //           break;
  //         case "finished":
  //         case "error":
  //           args.callback({ process: res.process });
  //           removeMessageSSEHandler();
  //           break;
  //       }
  //     }
  //   };
  //   portRef.current.onMessage.addListener(messageSSEHandler);
  //   // 向 background.js 发送 postMessage 请求：
  //   portRef.current.postMessage({
  //     url: args.url,
  //     type: FETCH_REQUEST_TYPE,
  //     headers: args.headers,
  //     body: args.body,
  //     query: args.query,
  //     instruct: args.instruct,
  //   });
  // }, []);
  // 提供的 stopSSE 函数，用于停止 SSE 请求
  const stopSSE = useCallback(() => {
    if (portRef.current) {
      portRef.current.postMessage({ instruct: "stop" });
    }
  }, []);

  // 检查端口连接状态
  const checkPortConnection = useCallback(() => {
    if (!portRef.current) {
      return false;
    }

    try {
      // 在 Manifest V3 中，检查端口是否仍然有效
      if (!portRef.current.name) {
        portRef.current = null;
        return false;
      }
      return true;
    } catch (error) {
      console.debug("SSE端口连接已断开:", error);
      portRef.current = null;
      return false;
    }
  }, []);

  // 提取SSE消息发送逻辑 - 先定义，避免提升问题
  const sendSSEMessage = useCallback((args: IFetchSSEArguments) => {
    if (!portRef.current) {
      console.error("SSE端口连接不存在");
      args.callback({ process: "error", msg: "连接失败" });
      return;
    }

    const removeMessageSSEHandler = () => {
      portRef.current?.onMessage.removeListener(messageSSEHandler);
    };

    // 定义处理来自 background.js 的 SSE 消息的函数
    const messageSSEHandler = (res: IFetchSSEResponseMessage) => {
      if (res.instruct === "stop") {
        args.callback({ process: res.process });
        removeMessageSSEHandler();
      } else {
        switch (res.process) {
          case "onmessage":
            args.callback({ ...res, process: res.process });
            break;
          case "onmessagefile":
            args.callback({ ...res, process: res.process });
            break;
          case "finished":
          case "cancel":
          case "error":
            if (res.process == "cancel") {
              // 不要直接断开连接，让background处理
              // portRef.current.disconnect();
              // handlePort();
            }
            args.callback({ process: res.process, msg: res.msg ? res.msg : "" });
            removeMessageSSEHandler();
            break;
        }
      }
    };

    try {
      portRef.current.onMessage.addListener(messageSSEHandler);

      // 向 background.js 发送请求
      portRef.current.postMessage({
        url: args.url,
        type: FETCH_REQUEST_TYPE,
        headers: args.headers,
        body: args.body,
        query: args.query,
        instruct: args.instruct,
      });
    } catch (error) {
      console.error("发送SSE消息失败:", error);
      removeMessageSSEHandler();
      args.callback({ process: "error", msg: "发送失败" });
    }
  }, []);

  // SSE 请求处理器
  const fetchSSE = useCallback((args: IFetchSSEArguments) => {
    // 检查并确保端口连接正常
    if (!checkPortConnection()) {
      console.debug("SSE端口连接不可用，尝试重新连接...");
      handlePort();

      // 等待连接建立后再发送消息
      const waitForConnection = () => {
        if (portRef.current) {
          sendSSEMessage(args);
        } else {
          // 如果连接仍未建立，延迟重试
          setTimeout(() => {
            if (portRef.current) {
              sendSSEMessage(args);
            } else {
              console.error("SSE端口连接失败，无法发送消息");
              args.callback({ process: "error", msg: "连接失败" });
            }
          }, 200);
        }
      };

      setTimeout(waitForConnection, 100);
      return;
    }

    sendSSEMessage(args);
  }, [checkPortConnection, handlePort, sendSSEMessage]);

  return { stopSSE, fetchSSE };
};
