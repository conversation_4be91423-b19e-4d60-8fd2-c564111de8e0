{"name": "sino-copilot-crx", "description": "一站式办公助手，企业办公效率的提升工具", "version": "1.0.4", "type": "module", "private": true, "author": {"name": "sino-bridge"}, "repository": "http://*************/aigc/sino-assisistant-crx-new.git", "engines": {"node": ">=18.17.1", "npm": ">=9.6.7", "yarn": ">=1.22.22", "pnpm": ">=8.15.6"}, "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:mv2:dev": "wxt build --mv2 --mode development", "build:mv2:prod": "wxt build --mv2 --mode production", "build:mv3:dev": "wxt build --mode development", "build:mv3:prod": "wxt build --mode production", "build:mv3:shenyang.prod": "wxt build --mode shenyang.production", "build:mv3:guokai.prod": "wxt build --mode guokai.production", "build:mv2:guojun.prod": "wxt build --mv2 --mode guojun.production", "build:mv3:guojun.prod": "wxt build --mode guojun.production", "build:mv2:zhongzheng.prod": "wxt build --mv2 --mode zhongzheng.production", "build:mv3:zhongzheng.prod": "wxt build --mode zhongzheng.production", "build:mv2:sanxingrenshou.prod": "wxt build --mv2 --mode sanxingrenshou.production", "build:mv3:sanxingrenshou.prod": "wxt build --mode sanxingrenshou.production", "build:mv2:shandonghuangjin.prod": "wxt build --mv2 --mode shandonghuangjin.production", "build:mv3:shandonghuangjin.prod": "wxt build --mode shandonghuangjin.production", "build:mv2:huawei.prod": "wxt build --mv2 --mode huawei.production", "build:mv3:huawei.prod": "wxt build --mode huawei.production", "build:mv2:dongzheng.prod": "wxt build --mv2 --mode dongzheng.production", "build:mv3:dongzheng.prod": "wxt build --mode dongzheng.production", "build:mv2:hexiejiankang.prod": "wxt build --mv2 --mode hexiejiankang.production", "build:mv3:hexiejiankang.prod": "wxt build --mode hexiejiankang.production", "build:mv2:haitongqihuo.prod": "wxt build --mv2 --mode haitongqihuo.production", "build:mv3:haitongqihuo.prod": "wxt build --mode haitongqihuo.production", "build:mv2:zhonghui.prod": "wxt build --mv2 --mode zhonghui.production", "build:mv3:zhonghui.prod": "wxt build --mode zhonghui.production", "build:mv3:huangjincaiwu.prod": "wxt build --mode huangjincaiwu.production", "build:firefox": "wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "tsc --noEmit", "postinstall": "wxt prepare", "prepare": "husky install"}, "dependencies": {"@ant-design/cssinjs": "^1.21.1", "@ant-design/icons": "^5.3.6", "@microsoft/fetch-event-source": "^2.0.1", "ahooks": "^3.7.5", "antd": "^5.20.0", "approx-string-match": "^2.0.0", "classnames": "^2.5.1", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "interactjs": "^1.10.27", "katex": "^0.16.11", "less": "^4.2.0", "lodash.debounce": "^4.0.3", "mermaid": "^11.6.0", "mqtt": "^5.10.3", "preact": "10.24.0", "process": "^0.11.10", "prosemirror-commands": "^1.5.2", "prosemirror-dropcursor": "^1.8.1", "prosemirror-gapcursor": "^1.3.2", "prosemirror-history": "^1.3.2", "prosemirror-inputrules": "^1.3.0", "prosemirror-keymap": "^1.2.2", "prosemirror-model": "^1.19.4", "prosemirror-schema-basic": "^1.2.2", "prosemirror-schema-list": "^1.3.0", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.32.7", "qs": "^6.13.0", "rc-mentions": "^2.11.1", "rc-textarea": "^1.6.3", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "react-mde": "^11.5.0", "react-quill": "^2.0.0", "react-router-dom": "^6.21.1", "react-syntax-highlighter": "^15.5.0", "rehype-katex": "^7.0.0", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "remark-mermaid": "^0.2.0", "scroll-into-view": "^1.8.2", "tiny-emitter": "^2.1.0", "typescript": "^5.2.2", "uuid": "^11.0.5", "webextension-polyfill": "^0.12.0"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/eslint-parser": "^7.23.10", "@sino-bridge/types-copilot": "^0.0.3", "@types/chrome": "^0.0.254", "@types/node": "^20.10.5", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@types/react-syntax-highlighter": "^15.5.11", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-config-alloy": "^5.1.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "husky": "^8.0.3", "less": "^4.2.0", "lint-staged": "^15.2.7", "npm-check": "^6.0.1", "prettier": "^3.2.5", "react-infinite-scroll-component": "^6.1.0", "vite": "^5.2.6", "vite-plugin-eslint": "^1.8.1", "vite-plugin-svgr": "^4.2.0", "wxt": "0.17.11"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,tsx,js}": ["eslint --fix", "prettier --write", "git add"]}}