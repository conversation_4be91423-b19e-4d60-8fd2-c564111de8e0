const fs = require('fs');
const crypto = require('crypto');

// 读取私钥 PEM 文件
const pem = fs.readFileSync('mykey_pkcs8.pem', 'utf8');

// 创建私钥对象
const privateKey = crypto.createPrivateKey(pem);

// 从私钥导出公钥 DER 格式（spki）
const publicKeyDer = privateKey.export({ type: 'pkcs8', format: 'der' });

// 使用 Node 18+ 推荐方式，先导出公钥对象
const publicKey = crypto.createPublicKey(privateKey);

// 导出公钥为 DER（spki）
const pubDer = publicKey.export({ type: 'spki', format: 'der' });

// 计算 SHA256 摘要
const hash = crypto.createHash('sha256').update(pubDer).digest();

// Chrome 扩展 ID 的编码表
const alphabet = 'abcdefghijklmnop';

// 转成 32 字符的 ID
const id = Array.from(hash.slice(0, 16))
  .map(b => alphabet[b >> 4] + alphabet[b & 15])
  .join('');

console.log('正确扩展ID:', id);
