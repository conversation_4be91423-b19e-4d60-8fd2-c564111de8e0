const fs = require('fs');
const crypto = require('crypto');
const { execSync } = require('child_process');

// 生成 RSA 密钥对
function generateRSAKeyPair() {
  console.log('正在生成 RSA 密钥对...');
  
  // 检查密钥是否已存在
  if (fs.existsSync('mykey_pkcs8.pem') && fs.existsSync('proof_key.pub')) {
    console.log('⚠️ 密钥文件已存在。如需重新生成，请先删除现有密钥文件。');
    return;
  }
  
  try {
    // 生成私钥
    execSync('openssl genpkey -algorithm RSA -pkeyopt rsa_keygen_bits:2048 -outform PEM -out mykey_pkcs8.pem');
    console.log('✅ 私钥已生成: mykey_pkcs8.pem');
    
    // 从私钥提取公钥
    execSync('openssl rsa -pubout -in mykey_pkcs8.pem -out mykey.pub');
    console.log('✅ 公钥已生成: mykey.pub');
    
    // 生成 proof 密钥对
    execSync('openssl genpkey -algorithm RSA -pkeyopt rsa_keygen_bits:2048 -outform PEM -out proof_key_pkcs8.pem');
    console.log('✅ Proof 私钥已生成: proof_key_pkcs8.pem');
    
    // 从 proof 私钥提取公钥
    execSync('openssl rsa -pubout -in proof_key_pkcs8.pem -out proof_key.pub');
    console.log('✅ Proof 公钥已生成: proof_key.pub');
    
    // 计算扩展 ID
    const pubKeyDer = execSync('openssl rsa -pubin -in mykey.pub -outform DER');
    const hash = crypto.createHash('sha256').update(pubKeyDer).digest();
    
    // Chrome 扩展 ID 的编码表
    const alphabet = 'abcdefghijklmnop';
    
    // 转成 32 字符的 ID
    const id = Array.from(hash.slice(0, 16))
      .map(b => alphabet[b >> 4] + alphabet[b & 15])
      .join('');
    
    console.log('✅ 扩展 ID:', id);
    
    // 保存扩展 ID 到文件
    fs.writeFileSync('extension_id.txt', id);
    console.log('✅ 扩展 ID 已保存到 extension_id.txt');
    
    // 创建 updates.xml 模板
    const updatesXml = `<?xml version='1.0' encoding='UTF-8'?>
<gupdate xmlns='http://www.google.com/update2/response' protocol='2.0'>
  <app appid='${id}'>
    <updatecheck codebase='https://copilot.sino-bridge.com:85/update/extension.crx' version='${require('../package.json').version}' />
  </app>
</gupdate>`;
    
    fs.writeFileSync('updates.xml', updatesXml);
    console.log('✅ updates.xml 模板已生成');
    
    console.log('\n🔐 重要提示:');
    console.log('1. 请安全保存 mykey_pkcs8.pem 和 proof_key_pkcs8.pem 私钥文件');
    console.log('2. 这些私钥用于签署所有未来版本的扩展');
    console.log('3. 如果丢失，将无法发布与现有扩展 ID 兼容的更新');
  } catch (error) {
    console.error('❌ 生成密钥失败:', error.message);
    process.exit(1);
  }
}

// 执行生成
generateRSAKeyPair();