const fs = require('fs');
const path = require('path');

// 验证 CRX 文件是否包含 proof 签名（替代方法）
function verifyCrxProofAlt(crxPath) {
  if (!fs.existsSync(crxPath)) {
    console.error(`❌ 错误: CRX 文件 ${crxPath} 不存在`);
    process.exit(1);
  }

  console.log(`🔍 正在检查 ${crxPath} 是否包含 proof 签名...`);
  
  try {
    // 读取 CRX 文件
    const crxBuffer = fs.readFileSync(crxPath);
    
    // 检查 CRX 魔数 (Cr24)
    const magic = crxBuffer.slice(0, 4).toString();
    if (magic !== 'Cr24') {
      console.error('❌ 不是有效的 CRX 文件 (缺少 Cr24 魔数)');
      return false;
    }
    
    // 读取版本号
    const version = crxBuffer.readUInt32LE(4);
    console.log(`📌 CRX 版本: ${version}`);
    
    if (version !== 3) {
      console.error('❌ 不是 CRX3 格式 (只有 CRX3 支持 proof 签名)');
      return false;
    }
    
    // 将整个文件转换为字符串（用于简单搜索）
    const crxString = crxBuffer.toString('utf8', 0, Math.min(crxBuffer.length, 10000));
    
    // 搜索与 proof 签名相关的字符串
    const proofKeywords = ['proof', 'Proof', 'PROOF'];
    let foundProof = false;
    
    for (const keyword of proofKeywords) {
      if (crxString.includes(keyword)) {
        console.log(`✅ 在 CRX 文件中找到关键字 "${keyword}"`);
        foundProof = true;
      }
    }
    
    if (foundProof) {
      console.log('✅ CRX 文件可能包含 proof 签名');
      return true;
    } else {
      console.error('❌ CRX 文件可能不包含 proof 签名 (未找到相关关键字)');
      
      // 显示文件的前 100 个字节，用于调试
      console.log('\n📋 文件头部数据 (前 100 个字节):');
      console.log(Buffer.from(crxBuffer.slice(0, 100)).toString('hex').match(/.{1,2}/g).join(' '));
      
      return false;
    }
  } catch (error) {
    console.error('❌ 验证失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行脚本，验证指定的 CRX 文件
if (require.main === module) {
  const args = process.argv.slice(2);
  if (args.length === 0) {
    console.error('❌ 错误: 请指定要验证的 CRX 文件路径');
    console.log('用法: node verify-proof-alt.cjs <crx文件路径>');
    process.exit(1);
  }
  
  verifyCrxProofAlt(args[0]);
}

module.exports = { verifyCrxProofAlt };