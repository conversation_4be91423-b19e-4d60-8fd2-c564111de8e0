const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 检查密钥是否存在
function checkKeysExist() {
  const privateKeyPath = 'mykey_pkcs8.pem';
  const proofKeyPath = 'proof_key.pub';
  
  if (!fs.existsSync(privateKeyPath)) {
    console.error('❌ 错误: 私钥文件 mykey_pkcs8.pem 不存在');
    console.log('请先运行 npm run generate-keys 生成密钥');
    process.exit(1);
  }
  
  if (!fs.existsSync(proofKeyPath)) {
    console.error('❌ 错误: proof 公钥文件 proof_key.pub 不存在');
    console.log('请先运行 npm run generate-keys 生成密钥');
    process.exit(1);
  }
  
  return true;
}

// 生成带 proof 签名的 CRX 包
function buildCrxWithProof() {
  // 检查密钥
  checkKeysExist();
  
  // 读取版本号
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const version = packageJson.version;
  
  console.log(`🔨 开始构建版本 ${version} 的 CRX 包...`);
  
  // 确保输出目录存在
  if (!fs.existsSync('.output')) {
    fs.mkdirSync('.output', { recursive: true });
  }
  
  // 构建 MV3 版本
  console.log('🔨 构建 MV3 版本...');
  try {
    execSync('npm run build:mv3:prod', { stdio: 'inherit' });
  } catch (error) {
    console.error('❌ 构建失败:', error.message);
    process.exit(1);
  }
  
  // 打包 CRX 文件
  console.log('📦 打包带 proof 签名的 CRX 文件...');
  try {
    execSync(
      'npx crx3 -p mykey_pkcs8.pem -o .output/extension-with-proof.crx .output/chrome-mv3 --proof-key=proof_key.pub',
      { stdio: 'inherit' }
    );
    
    // 创建带版本号的副本
    fs.copyFileSync(
      '.output/extension-with-proof.crx',
      `.output/extension-${version}-with-proof.crx`
    );
    
    console.log(`✅ CRX 文件已生成:`);
    console.log(`   - .output/extension-with-proof.crx`);
    console.log(`   - .output/extension-${version}-with-proof.crx`);
  } catch (error) {
    console.error('❌ 打包失败:', error.message);
    process.exit(1);
  }
}

// 执行构建
buildCrxWithProof();