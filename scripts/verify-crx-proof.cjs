const fs = require('fs');
const crypto = require('crypto');

// 验证 CRX 文件是否包含 proof 签名
function verifyCrxProof(crxPath) {
  console.log(`🔍 验证 CRX 文件: ${crxPath}`);
  
  if (!fs.existsSync(crxPath)) {
    console.error(`❌ CRX 文件不存在: ${crxPath}`);
    return false;
  }
  
  const crxData = fs.readFileSync(crxPath);
  
  // 检查魔数
  const magic = crxData.slice(0, 4).toString('ascii');
  if (magic !== 'Cr24') {
    console.error('❌ 不是有效的 CRX 文件 (魔数错误)');
    return false;
  }
  
  // 读取版本号
  const version = crxData.readUInt32LE(4);
  console.log(`📋 CRX 版本: ${version}`);
  
  if (version !== 3) {
    console.error('❌ 不是 CRX3 格式');
    return false;
  }
  
  // 读取头部长度
  const headerLength = crxData.readUInt32LE(8);
  console.log(`📋 头部长度: ${headerLength} 字节`);
  
  // 提取头部数据
  const headerData = crxData.slice(12, 12 + headerLength);
  
  // 解析头部
  try {
    const parsedHeader = parseProtobufHeader(headerData);
    
    console.log('✅ CRX 头部解析成功');
    console.log(`📋 主签名数据长度: ${parsedHeader.mainSignature ? parsedHeader.mainSignature.length : 0} 字节`);
    console.log(`📋 Proof 签名数据长度: ${parsedHeader.proofSignature ? parsedHeader.proofSignature.length : 0} 字节`);
    
    if (parsedHeader.proofSignature && parsedHeader.proofSignature.length > 0) {
      console.log('🔐 ✅ 包含 proof 签名');
      return true;
    } else {
      console.log('⚠️  不包含 proof 签名');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 头部解析失败:', error.message);
    return false;
  }
}

// 简单的 protobuf 解析器
function parseProtobufHeader(data) {
  const result = {
    mainSignature: null,
    proofSignature: null
  };
  
  let offset = 0;
  
  while (offset < data.length) {
    // 读取 tag
    const { value: tag, bytesRead: tagBytes } = readVarint(data, offset);
    offset += tagBytes;
    
    const fieldNumber = tag >> 3;
    const wireType = tag & 0x07;
    
    if (wireType === 2) { // Length-delimited
      const { value: length, bytesRead: lengthBytes } = readVarint(data, offset);
      offset += lengthBytes;
      
      const fieldData = data.slice(offset, offset + length);
      offset += length;
      
      if (fieldNumber === 1) {
        // 主签名数据
        result.mainSignature = fieldData;
      } else if (fieldNumber === 2) {
        // Proof 签名数据
        result.proofSignature = fieldData;
      }
    } else {
      // 跳过其他类型的字段
      break;
    }
  }
  
  return result;
}

// 读取 varint
function readVarint(buffer, offset) {
  let value = 0;
  let shift = 0;
  let bytesRead = 0;
  
  while (offset + bytesRead < buffer.length) {
    const byte = buffer[offset + bytesRead];
    bytesRead++;
    
    value |= (byte & 0x7F) << shift;
    
    if ((byte & 0x80) === 0) {
      break;
    }
    
    shift += 7;
  }
  
  return { value, bytesRead };
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  
  if (args.length !== 1) {
    console.error('用法: node verify-crx-proof.cjs <CRX文件>');
    console.error('示例: node verify-crx-proof.cjs ./extension.crx');
    process.exit(1);
  }
  
  const crxPath = args[0];
  const hasProof = verifyCrxProof(crxPath);
  
  if (hasProof) {
    console.log('🎉 验证成功: CRX 文件包含 proof 签名');
    process.exit(0);
  } else {
    console.log('❌ 验证失败: CRX 文件不包含 proof 签名');
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { verifyCrxProof };
