const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 使用 Chrome 官方工具创建带 proof 签名的 CRX 文件
function createCrxWithProof(extensionDir, privateKeyPath, proofKeyPath, outputPath) {
  console.log('🔧 使用 Chrome 官方工具创建带 proof 签名的 CRX 文件...');

  try {
    // 检查 Chrome 是否安装
    let chromePath;
    try {
      // 尝试不同的 Chrome 路径
      const possiblePaths = [
        '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
        '/usr/bin/google-chrome',
        '/usr/bin/google-chrome-stable',
        'google-chrome',
        'chrome'
      ];

      for (const testPath of possiblePaths) {
        try {
          execSync(`"${testPath}" --version`, { stdio: 'pipe' });
          chromePath = testPath;
          break;
        } catch (e) {
          // 继续尝试下一个路径
        }
      }

      if (!chromePath) {
        throw new Error('Chrome not found');
      }

      console.log(`✅ 找到 Chrome: ${chromePath}`);
    } catch (error) {
      throw new Error('未找到 Chrome 浏览器。请确保已安装 Google Chrome。');
    }

    // 检查私钥文件是否存在
    if (!fs.existsSync(privateKeyPath)) {
      throw new Error(`私钥文件不存在: ${privateKeyPath}`);
    }

    // 注意：不检查 proof 私钥文件，因为 Chrome 官方工具不支持
    console.log('ℹ️  Chrome 官方工具不支持 proof 签名，将忽略 proof 私钥参数');

    // 确保输出目录存在
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // 使用 Chrome 的 --pack-extension 命令创建 CRX
    console.log('📦 使用 Chrome 打包扩展...');
    const command = `"${chromePath}" --headless --disable-gpu --pack-extension="${path.resolve(extensionDir)}" --pack-extension-key="${path.resolve(privateKeyPath)}"`;

    console.log(`执行命令: ${command}`);
    execSync(command, { stdio: 'inherit' });

    // Chrome 会在扩展目录的父目录创建 .crx 文件
    const defaultCrxPath = path.resolve(extensionDir) + '.crx';

    if (!fs.existsSync(defaultCrxPath)) {
      throw new Error(`Chrome 未能创建 CRX 文件: ${defaultCrxPath}`);
    }

    // 移动到指定位置
    if (defaultCrxPath !== path.resolve(outputPath)) {
      fs.copyFileSync(defaultCrxPath, outputPath);
      fs.unlinkSync(defaultCrxPath);
    }

    const stats = fs.statSync(outputPath);
    console.log(`✅ CRX 文件已创建: ${outputPath}`);
    console.log(`📊 文件大小: ${Math.round(stats.size / 1024)} KB`);

    // 注意：Chrome 的 --pack-extension 不直接支持 proof 签名
    // 这里我们创建了基本的 CRX 文件，但没有 proof 签名
    console.log('⚠️  注意: Chrome 官方工具不支持 proof 签名，创建的是标准 CRX 文件');

  } catch (error) {
    console.error('❌ 创建 CRX 文件失败:', error.message);
    throw error;
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2);

  if (args.length !== 4) {
    console.error('用法: node create-crx-with-proof.cjs <扩展目录> <私钥文件> <proof私钥文件> <输出CRX文件>');
    console.error('示例: node create-crx-with-proof.cjs ./dist ./private-key.pem ./proof-key.pem ./extension.crx');
    console.error('');
    console.error('注意: 此脚本使用 Chrome 官方工具，不支持 proof 签名');
    console.error('      proof 私钥参数会被忽略，但为了兼容性仍需提供');
    process.exit(1);
  }

  const [extensionDir, privateKeyPath, proofKeyPath, outputPath] = args;

  // 检查输入文件是否存在
  if (!fs.existsSync(extensionDir)) {
    console.error(`❌ 扩展目录不存在: ${extensionDir}`);
    process.exit(1);
  }

  if (!fs.existsSync(privateKeyPath)) {
    console.error(`❌ 私钥文件不存在: ${privateKeyPath}`);
    process.exit(1);
  }

  // 注意：proof 私钥文件检查被移除，因为 Chrome 官方工具不支持
  console.log('ℹ️  注意: Chrome 官方工具不支持 proof 签名，proof 私钥参数将被忽略');

  try {
    createCrxWithProof(extensionDir, privateKeyPath, proofKeyPath, outputPath);
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { createCrxWithProof };
