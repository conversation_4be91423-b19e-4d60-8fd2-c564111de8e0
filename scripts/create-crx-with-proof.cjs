const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { execSync } = require('child_process');

// CRX3 格式常量
const CRX3_MAGIC = 'Cr24';
const CRX3_VERSION = 3;

// 手动创建带 proof 签名的 CRX 文件
function createCrxWithProof(extensionDir, privateKeyPath, proofKeyPath, outputPath) {
  console.log('🔧 手动创建带 proof 签名的 CRX 文件...');

  try {
    // 1. 创建 ZIP 文件
    const tempZipPath = path.join(path.dirname(outputPath), 'temp-extension.zip');
    console.log('📦 创建临时 ZIP 文件...');

    // 检查是否有 zip 命令
    try {
      execSync('which zip', { stdio: 'pipe' });
    } catch (error) {
      throw new Error('系统未安装 zip 命令，请先安装 zip 工具');
    }

    execSync(`cd "${extensionDir}" && zip -r -9 "${path.resolve(tempZipPath)}" .`, { stdio: 'inherit' });

    // 2. 读取私钥
    console.log('🔑 读取私钥...');
    if (!fs.existsSync(privateKeyPath)) {
      throw new Error(`私钥文件不存在: ${privateKeyPath}`);
    }
    const privateKeyPem = fs.readFileSync(privateKeyPath, 'utf8');
    const privateKey = crypto.createPrivateKey(privateKeyPem);

    // 3. 读取 proof 私钥
    console.log('🔑 读取 proof 私钥...');
    if (!fs.existsSync(proofKeyPath)) {
      throw new Error(`Proof 私钥文件不存在: ${proofKeyPath}`);
    }
    const proofKeyPem = fs.readFileSync(proofKeyPath, 'utf8');
    const proofPrivateKey = crypto.createPrivateKey(proofKeyPem);

    // 4. 获取公钥
    const publicKey = crypto.createPublicKey(privateKey);
    const proofPublicKey = crypto.createPublicKey(proofPrivateKey);

    // 5. 导出公钥为 DER 格式
    const publicKeyDer = publicKey.export({ type: 'spki', format: 'der' });
    const proofPublicKeyDer = proofPublicKey.export({ type: 'spki', format: 'der' });

    // 6. 读取 ZIP 文件内容
    const zipData = fs.readFileSync(tempZipPath);

    // 7. 创建签名数据
    console.log('✍️ 创建主签名...');
    const signature = crypto.sign('sha256', zipData, privateKey);

    // 8. 创建 proof 签名
    console.log('✍️ 创建 proof 签名...');
    const proofSignature = crypto.sign('sha256', zipData, proofPrivateKey);

    // 9. 构建 CRX 头部
    console.log('🔨 构建 CRX 头部...');
    const header = createCrxHeader(publicKeyDer, signature, proofPublicKeyDer, proofSignature);

    // 10. 写入 CRX 文件
    console.log('💾 写入 CRX 文件...');
    const crxBuffer = Buffer.concat([
      Buffer.from(CRX3_MAGIC, 'ascii'),  // 魔数
      Buffer.alloc(4),                   // 版本号 (稍后填入)
      Buffer.alloc(4),                   // 头部长度 (稍后填入)
      header,                            // 头部数据
      zipData                            // ZIP 数据
    ]);

    // 填入版本号和头部长度
    crxBuffer.writeUInt32LE(CRX3_VERSION, 4);
    crxBuffer.writeUInt32LE(header.length, 8);

    // 确保输出目录存在
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    fs.writeFileSync(outputPath, crxBuffer);

    // 11. 清理临时文件
    fs.unlinkSync(tempZipPath);

    console.log(`✅ 带 proof 签名的 CRX 文件已创建: ${outputPath}`);
    console.log(`📊 文件大小: ${Math.round(crxBuffer.length / 1024)} KB`);
    console.log(`📊 头部大小: ${header.length} 字节`);
    console.log(`🔐 包含主签名和 proof 签名`);

  } catch (error) {
    console.error('❌ 创建 CRX 文件失败:', error.message);
    throw error;
  }
}

// 创建 CRX 头部 (包含 proof 签名)
function createCrxHeader(publicKeyDer, signature, proofPublicKeyDer, proofSignature) {
  // 创建主签名数据
  const mainSignedData = Buffer.concat([
    createProtobufField(1, 2, publicKeyDer),    // 公钥
    createProtobufField(2, 2, signature)        // 签名
  ]);

  // 创建 proof 签名数据
  const proofSignedData = Buffer.concat([
    createProtobufField(1, 2, proofPublicKeyDer), // proof 公钥
    createProtobufField(2, 2, proofSignature)     // proof 签名
  ]);

  // 组合头部 - 包含主签名和 proof 签名
  const header = Buffer.concat([
    createProtobufField(1, 2, mainSignedData),   // 主签名数据 (tag 1)
    createProtobufField(2, 2, proofSignedData)   // proof 签名数据 (tag 2)
  ]);

  return header;
}

// 创建 protobuf 字段
function createProtobufField(fieldNumber, wireType, data) {
  const tag = (fieldNumber << 3) | wireType;
  const tagBuffer = encodeVarint(tag);

  if (wireType === 2) { // Length-delimited
    const lengthBuffer = encodeVarint(data.length);
    return Buffer.concat([tagBuffer, lengthBuffer, data]);
  }

  return Buffer.concat([tagBuffer, data]);
}

// 编码 varint
function encodeVarint(value) {
  const result = [];
  while (value >= 0x80) {
    result.push((value & 0xFF) | 0x80);
    value >>>= 7;
  }
  result.push(value & 0xFF);
  return Buffer.from(result);
}

// 主函数
function main() {
  const args = process.argv.slice(2);

  if (args.length !== 4) {
    console.error('用法: node create-crx-with-proof.cjs <扩展目录> <私钥文件> <proof私钥文件> <输出CRX文件>');
    console.error('示例: node create-crx-with-proof.cjs .output/chrome-mv3 ./mykey_pkcs8.pem ./proof_key_pkcs8.pem ./extension.crx');
    console.error('');
    console.error('注意: 此脚本手动创建带 proof 签名的 CRX 文件');
    process.exit(1);
  }

  const [extensionDir, privateKeyPath, proofKeyPath, outputPath] = args;

  // 检查输入文件是否存在
  if (!fs.existsSync(extensionDir)) {
    console.error(`❌ 扩展目录不存在: ${extensionDir}`);
    process.exit(1);
  }

  if (!fs.existsSync(privateKeyPath)) {
    console.error(`❌ 私钥文件不存在: ${privateKeyPath}`);
    process.exit(1);
  }

  if (!fs.existsSync(proofKeyPath)) {
    console.error(`❌ Proof 私钥文件不存在: ${proofKeyPath}`);
    process.exit(1);
  }

  try {
    createCrxWithProof(extensionDir, privateKeyPath, proofKeyPath, outputPath);
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { createCrxWithProof };
