const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');
const crypto = require('crypto');

// 验证 CRX 文件是否包含 proof 签名
function verifyCrxProof(crxPath) {
  if (!fs.existsSync(crxPath)) {
    console.error(`❌ 错误: CRX 文件 ${crxPath} 不存在`);
    process.exit(1);
  }

  console.log(`🔍 正在检查 ${crxPath} 是否包含 proof 签名...`);

  try {
    // 创建临时目录
    const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'crx-verify-'));

    // 读取 CRX 文件
    const crxBuffer = fs.readFileSync(crxPath);

    // 检查 CRX 魔数 (Cr24)
    const magic = crxBuffer.slice(0, 4).toString();
    if (magic !== 'Cr24') {
      console.error('❌ 不是有效的 CRX 文件 (缺少 Cr24 魔数)');
      return false;
    }

    // 读取版本号
    const version = crxBuffer.readUInt32LE(4);
    console.log(`📌 CRX 版本: ${version}`);

    if (version !== 3) {
      console.error('❌ 不是 CRX3 格式 (只有 CRX3 支持 proof 签名)');
      return false;
    }

    // 读取头部长度
    const headerSize = crxBuffer.readUInt32LE(8);
    console.log(`📌 头部大小: ${headerSize} 字节`);

    // 读取头部数据
    const headerData = crxBuffer.slice(12, 12 + headerSize);

    // 将 CRX 文件解压到临时目录
    const tempCrxPath = path.join(tempDir, 'extension.crx');
    fs.writeFileSync(tempCrxPath, crxBuffer);

    try {
      // 尝试使用 unzip 解压 CRX 文件
      execSync(`unzip -q -o ${tempCrxPath} -d ${tempDir}`, { stdio: 'ignore' });

      // 检查是否存在清单文件
      const manifestPath = path.join(tempDir, 'manifest.json');
      if (fs.existsSync(manifestPath)) {
        const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
        console.log(`📌 扩展名称: ${manifest.name}`);
        console.log(`📌 扩展版本: ${manifest.version}`);
      }
    } catch (error) {
      // 解压失败，忽略错误
      console.log('⚠️ 无法解压 CRX 文件，继续检查二进制数据');
    }

    // 在二进制数据中搜索 "proof" 字符串
    const proofKeyword = Buffer.from('proof');
    let foundProof = false;
    let proofPosition = -1;

    for (let i = 0; i < headerData.length - proofKeyword.length; i++) {
      let match = true;
      for (let j = 0; j < proofKeyword.length; j++) {
        if (headerData[i + j] !== proofKeyword[j]) {
          match = false;
          break;
        }
      }
      if (match) {
        foundProof = true;
        proofPosition = i;
        break;
      }
    }

    // 清理临时目录
    try {
      fs.rmSync(tempDir, { recursive: true, force: true });
    } catch (error) {
      // 忽略清理错误
    }

    if (foundProof) {
      console.log(`✅ CRX 文件包含 proof 签名！(在头部数据偏移量 ${proofPosition} 处找到)`);

      // 计算并显示头部数据的 SHA-256 哈希，用于验证
      const headerHash = crypto.createHash('sha256').update(headerData).digest('hex');
      console.log(`📌 头部数据 SHA-256: ${headerHash}`);

      return true;
    } else {
      console.error('❌ CRX 文件不包含 proof 签名 (在头部数据中未找到 "proof" 字符串)');
      return false;
    }
  } catch (error) {
    console.error('❌ 验证失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行脚本，验证指定的 CRX 文件
if (require.main === module) {
  const args = process.argv.slice(2);
  if (args.length === 0) {
    console.error('❌ 错误: 请指定要验证的 CRX 文件路径');
    console.log('用法: node verify-proof.cjs <crx文件路径>');
    process.exit(1);
  }

  verifyCrxProof(args[0]);
}

module.exports = { verifyCrxProof };