import { addComment, delComment, pageComments } from "@/api/comment.ts";
import { baiduocr } from "@/api/tools/ocr.ts";
import { createShortUrl } from "@/api/tools/code.ts";
import { getSceneList } from "@/api/tools/index.ts";
import {
  addPrompt,
  cancelPromptCollectionStatus,
  delPrompt,
  editPrompt,
  pagePrompts,
  updatePromptCollectionStatus,
  updatePromptShareStatus,
} from "@/api/prompt.ts";
import { getAcctIns } from "@/api/ins.ts";
import {
  addKnowledge,
  businessUpdateTmpFileConversationRela,
  delFile,
  delKnowledge,
  downLoadFile,
  editKnowledge,
  getAllData,
  memberShare,
  memberList,
  getItemDetail,
  libBaseInfoAddUrl,
  libBaseInfoDelUrl,
  libBaseInfoGetUrl,
  listAgents,
  uploadKnowledgeFile,
  getKnowledgeData,
  getTeamList,
  addNoteKnowledge,
  editEnterprise,
  delEnterpriseKnowledge,
  settingInfo,
  editSetting,
  clientCrawSave,
  changePermission,
  ocrAdd,
  getKnowledgeTeam,
} from "@/api/knowdge.ts";

import { getQuestionList, recommendQuestion, submittQuestion } from "@/api/answerTips";

import {
  addNote,
  addNotePromptRela,
  delNote,
  delNoteObjRela,
  editNote,
  listNote,
  listNoteRela,
  markNoteRead,
  pageNote,
  queryNote,
  shareNote,
  notesGroupTree,
  addNotesGroup,
  editNotesGroup,
  delNotesGroup,
  notesGroupPage,
  getGroupInfo,
  flatList,
  addrelation,
  noteRelationRemove,
  notesGroupDissolve,
} from "@/api/note.ts";
import { myNoticeList, noticeSuccessCallBack } from "@/api/message.ts";
import { IFetchRequestMessage } from "@/types/message";
import { templateList } from "@/api/template.ts"
import { apiCallManager } from "@/utils/debounce.ts";
import {
  conversationRename,
  deleteConversationById,
  getConversation,
  getConversationHistoryList,
  stopMessage,
  uploadChatFile,
} from "@/api/chat.ts";
import {
  getCurrentUserInfo,
  loginByAccount,
  refreshGetToken,
  switchTenantAndOrg,
  logout,
  getResource,
  getLatestVersion,
  getBaseEmployee,
} from "@/api/user.ts";
import { wordList, addWord, editWord } from "@/api/smartMenu.ts";

const apiList = {
  loginByAccount,
  refreshGetToken,
  switchTenantAndOrg,
  getResource,
  getCurrentUserInfo,
  logout,
  addComment,
  delComment,
  pageComments,
  getBaseEmployee,
  // 截图OCR
  baiduocr,
  // 提示词
  pagePrompts,
  delPrompt,
  updatePromptCollectionStatus,
  cancelPromptCollectionStatus,
  updatePromptShareStatus,
  addPrompt,
  editPrompt,
  pageNote,
  listNote,
  addNote,
  addNotePromptRela,
  editNote,
  delNote,
  queryNote,
  shareNote,
  markNoteRead,
  listNoteRela,
  delNoteObjRela,
  stopMessage,
  uploadChatFile,
  notesGroupTree,
  addNotesGroup,
  editNotesGroup,
  delNotesGroup,
  notesGroupPage,
  getGroupInfo,
  flatList,
  addrelation,
  noteRelationRemove,
  notesGroupDissolve,
  // 设置
  wordList,
  addWord,
  editWord,

  // 知识库
  getAllData,
  addKnowledge,
  delKnowledge,
  getKnowledgeData,
  editKnowledge,
  editEnterprise,
  delEnterpriseKnowledge,
  uploadKnowledgeFile,
  ocrAdd,
  getKnowledgeTeam,
  getItemDetail,
  memberShare,
  memberList,
  changePermission,
  delFile,
  downLoadFile,
  settingInfo,
  editSetting,
  clientCrawSave,
  getConversationHistoryList,
  getConversation,
  deleteConversationById,
  conversationRename,
  libBaseInfoAddUrl,
  libBaseInfoGetUrl,
  libBaseInfoDelUrl,
  listAgents,
  businessUpdateTmpFileConversationRela,
  getTeamList,
  addNoteKnowledge,
  // 模版
  templateList,
  // 站内消息
  myNoticeList,
  noticeSuccessCallBack,

  recommendQuestion,
  getQuestionList,
  submittQuestion,
  createShortUrl,
  // 指令
  getAcctIns,
  // 场景
  getSceneList,
  getLatestVersion,
};

/** 普通网络请求端口的名称 */
export const FETCH_PORT_NAME = "fetch";
/** 网络请求消息类型 */
export const FETCH_REQUEST_TYPE = "request";
/** 网络响应消息类型 */
export const FETCH_RESPONSE_TYPE = "response";
const base64ToFile = (base64, fileName) => {
  // 检查 Base64 字符串格式
  const [prefix, base64Data] = base64.split(",");
  if (!base64Data) {
    throw new Error("Invalid Base64 string");
  }
  // 提取 MIME 类型
  const mimeType = prefix.match(/data:(.*?);base64/)?.[1] || "";
  // 解码 Base64 为二进制字符串
  const binaryString = atob(base64Data);
  // 创建二进制数组
  const binaryArray = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    binaryArray[i] = binaryString.charCodeAt(i);
  }
  // 创建 File 对象
  return new File([binaryArray], fileName, { type: mimeType });
};
import { swManager } from "@/utils/serviceWorkerManager";

const registerFetchMessageHandler = () => {
  // 使用状态管理器检查是否已注册
  if (swManager.isInitialized('fetchHandlerRegistered')) {
    console.debug("Fetch消息处理器已经注册，跳过重复注册");
    return;
  }

  console.debug("开始注册Fetch消息处理器...");

  const handleFetchConnected = () => {
    browser.runtime.onConnect.addListener((port) => {
      if (port.name !== FETCH_PORT_NAME) return;
      console.debug(`${FETCH_PORT_NAME} 端口已建立连接，连接ID: ${port.sender?.tab?.id || 'unknown'}`);

      port.onMessage.addListener((msg: IFetchRequestMessage) => {
        console.debug(`background ${FETCH_PORT_NAME} 收到了消息`, msg);
        if (msg.type === FETCH_REQUEST_TYPE) {
          let data = {};
          if (msg.file) {
            let fileData: any = msg.params;
            const formData = new FormData();
            if (fileData?.remoteFileUrl) { // 写作模版需要上传dify但是没有文件信息
              for (const key in fileData) {
                formData.append(key, fileData[key]);
              }
            } else {
              const file = base64ToFile(fileData.fileStr, fileData.fileName);
              formData.append("file", file); // 将文件附加到 FormData
              for (const key in fileData) {
                if (key !== "fileStr" && key !== "fileName") {
                  formData.append(key, fileData[key]);
                }
              }
            }
            data = formData;
          } else {
            data = msg.params;
          }

          // 防止重复调用API
          if (!apiList[msg.api]) {
            console.error(`API ${msg.api} 不存在`);
            return;
          }

          // 使用API调用管理器防止重复调用
          apiCallManager.call(msg.api, data, apiList[msg.api])
            .then((res) => {
              console.debug("background从服务端接受到Web响应：", res);
              try {
                port.postMessage({
                  api: msg.api,
                  type: FETCH_RESPONSE_TYPE,
                  response: res,
                });
              } catch (error) {
                console.debug("页面与接收消息的端口可能被主动销毁了", error);
              }
            })
            .catch((error) => {
              console.error(`API ${msg.api} 调用失败:`, error);
              try {
                port.postMessage({
                  api: msg.api,
                  type: FETCH_RESPONSE_TYPE,
                  response: { code: 500, msg: "请求失败", error: error.message },
                });
              } catch (postError) {
                console.debug("发送错误响应失败:", postError);
              }
            });
        }
      });

      port.onDisconnect.addListener((port) => {
        console.debug(`content主动断开了 ${FETCH_PORT_NAME} 端口的连接`);
      });
    });
  };

  // 在安装时与启动时注册消息响应事件，确保浏览器进程全部关闭再打开后连接依然可以打开
  handleFetchConnected();

  // 使用状态管理器标记已注册
  swManager.markInitialized('fetchHandlerRegistered', true);
  console.debug("Fetch消息处理器注册完成");
};

export default registerFetchMessageHandler;
